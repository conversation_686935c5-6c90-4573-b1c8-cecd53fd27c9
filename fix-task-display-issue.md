# 修复任务选择器显示UUID问题

## 问题分析总结

### 1. 确认的工作正常的部分
- 数据库schema中tasks表定义正确，使用`title`字段
- DatabaseManager.getTasks()方法正确返回title字段
- Tasks页面表格正确显示任务标题
- TaskKanbanView正确显示任务标题  
- Pomodoro组件中的任务选择器代码看起来正确

### 2. 可能的问题源头
1. **数据库中任务记录的title字段为空或null**
2. **任务创建时没有正确设置title**
3. **数据映射过程中title被覆盖**
4. **某个特定组件错误地显示ID而不是title**

## 修复方案

### 方案1: 添加调试日志
在获取任务数据的关键位置添加日志，确认数据结构。

### 方案2: 修复数据库查询
确保getTasks()方法正确处理title字段。

### 方案3: 修复任务选择器
在任务选择器组件中添加防护逻辑。

### 方案4: 修复任务创建
确保创建任务时正确设置title字段。