import Database from 'better-sqlite3';
import { join } from 'path';
import { homedir } from 'os';
import { randomUUID } from 'crypto';

// 查找数据库文件的可能位置
const possiblePaths = [
  join(homedir(), 'Library/Application Support/FocusOS/focusos.db'), // macOS
  join(process.cwd(), 'focusos.db'),
  join(process.cwd(), 'userData', 'focusos.db')
];

let dbPath = null;
for (const path of possiblePaths) {
  try {
    const db = new Database(path);
    db.close();
    dbPath = path;
    break;
  } catch {
    continue;
  }
}

if (!dbPath) {
  // 如果没有找到现有数据库，创建一个新的
  dbPath = join(process.cwd(), 'focusos.db');
  console.log('创建新数据库:', dbPath);
}

console.log('使用数据库:', dbPath);

try {
  const db = new Database(dbPath);
  
  // 创建测试目标
  const goalId = randomUUID();
  const goal = {
    id: goalId,
    user_id: 'default-user',
    name: '测试目标',
    description: '这是一个用于测试的目标',
    type: 'short-term',
    why_power: '为了测试应用功能',
    domains: JSON.stringify(['测试']),
    status: 'active',
    level: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  // 插入目标
  const insertGoal = db.prepare(`
    INSERT OR REPLACE INTO goals (
      id, user_id, name, description, type, why_power, domains, 
      status, level, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  insertGoal.run(
    goal.id, goal.user_id, goal.name, goal.description, goal.type,
    goal.why_power, goal.domains, goal.status, goal.level,
    goal.created_at, goal.updated_at
  );
  
  console.log('创建测试目标:', goal.name);
  
  // 创建测试任务
  const tasks = [
    {
      id: randomUUID(),
      goal_id: goalId,
      title: '完成项目文档',
      description: '编写和完善项目的技术文档',
      estimated_time: 120,
      actual_time: 0,
      priority: 'high',
      status: 'todo',
      completion_percentage: 0,
      tags: JSON.stringify(['文档', '重要']),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: randomUUID(),
      goal_id: goalId,
      title: '代码审查',
      description: '审查团队成员提交的代码',
      estimated_time: 60,
      actual_time: 0,
      priority: 'medium',
      status: 'todo',
      completion_percentage: 0,
      tags: JSON.stringify(['代码', '审查']),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: randomUUID(),
      goal_id: goalId,
      title: '修复已知bug',
      description: '修复在测试中发现的bug',
      estimated_time: 90,
      actual_time: 0,
      priority: 'high',
      status: 'in-progress',
      completion_percentage: 30,
      tags: JSON.stringify(['bug修复', 'QA']),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];
  
  // 插入任务
  const insertTask = db.prepare(`
    INSERT OR REPLACE INTO tasks (
      id, goal_id, title, description, estimated_time, actual_time, 
      priority, status, completion_percentage, tags, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  tasks.forEach(task => {
    insertTask.run(
      task.id, task.goal_id, task.title, task.description, task.estimated_time,
      task.actual_time, task.priority, task.status, task.completion_percentage,
      task.tags, task.created_at, task.updated_at
    );
    console.log('创建测试任务:', task.title);
  });
  
  console.log('\n测试数据创建完成!');
  console.log('目标数量:', 1);
  console.log('任务数量:', tasks.length);
  
  db.close();
} catch (error) {
  console.error('创建测试数据失败:', error);
}