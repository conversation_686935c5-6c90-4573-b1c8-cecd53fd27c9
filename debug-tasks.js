import Database from 'better-sqlite3';
import { join } from 'path';
import { homedir } from 'os';

// 查找数据库文件的可能位置
const possiblePaths = [
  join(homedir(), 'Library/Application Support/FocusOS/focusos.db'), // macOS
  join(process.cwd(), 'focusos.db'),
  join(process.cwd(), 'userData', 'focusos.db')
];

let dbPath = null;
for (const path of possiblePaths) {
  try {
    const db = new Database(path);
    db.close();
    dbPath = path;
    break;
  } catch {
    continue;
  }
}

console.log('数据库路径:', dbPath);

if (!dbPath) {
  console.log('未找到数据库文件');
  process.exit(1);
}

try {
  const db = new Database(dbPath);
  
  // 检查 tasks 表结构
  console.log('\n=== Tasks 表结构 ===');
  const columns = db.prepare('PRAGMA table_info(tasks)').all();
  columns.forEach(col => {
    console.log(`${col.name}: ${col.type}`);
  });
  
  // 查询现有任务数据
  console.log('\n=== 现有任务数据 ===');
  const tasks = db.prepare('SELECT * FROM tasks LIMIT 3').all();
  
  if (tasks.length === 0) {
    console.log('没有找到任务数据');
  } else {
    tasks.forEach((task, index) => {
      console.log(`任务 ${index + 1}:`, task);
    });
  }
  
  db.close();
} catch (error) {
  console.error('数据库操作失败:', error.message);
}