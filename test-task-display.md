# 任务选择器显示UUID问题分析

## 问题描述
任务选择器中显示的是UUID而不是任务名称

## 发现的问题

### 1. 数据库结构分析
- schema.sql中定义的tasks表使用`goal_id`字段（旧结构）
- DatabaseManager.ts中有兼容新旧两种结构的逻辑：
  - 旧结构：`goal_id as goalNodeId` 
  - 新结构：`parent_id as parentId`

### 2. 前端代码问题
在Pomodoro.tsx中，任务选择器正确显示了`task.title`：
```tsx
{filteredTasks.map(task => (
  <Option key={task.id} value={task.id}>
    <div>
      <div>{task.title}</div>  // 这里正确显示了title
      {!selectedGoalId && (
        <Text type="secondary" style={{ fontSize: '10px' }}>
          {goals.find(g => g.id === (task as any).goalNodeId)?.name || '无目标'}
        </Text>
      )}
    </div>
  </Option>
))}
```

### 3. 潜在问题位置
可能的问题：
1. 任务数据从数据库加载时，title字段为空或未正确映射
2. 数据库中实际存储的任务数据有问题
3. getTasks()方法返回的数据格式问题

### 4. 需要验证的点
1. 数据库中实际的任务数据是什么样的
2. getTasks()方法实际返回的数据结构
3. 前端接收到的任务数据格式