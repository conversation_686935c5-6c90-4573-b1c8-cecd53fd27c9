import Database from 'better-sqlite3';
import { app } from 'electron';
import { join, dirname } from 'path';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';

export class DatabaseManager {
  private db: Database.Database;
  private static instance: DatabaseManager;

  private constructor() {
    const dbPath = join(app.getPath('userData'), 'focusos.db');
    this.db = new Database(dbPath);
    this.initialize();
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  private initialize() {
    // 启用WAL模式以提高性能
    this.db.pragma('journal_mode = WAL');
    
    // 启用外键约束
    this.db.pragma('foreign_keys = ON');
    
    // 创建表结构
    this.createTables();
    
    // 运行数据库迁移
    this.runMigrations();
  }

  private createTables() {
    try {
      const schema = `
        -- 目标表
        CREATE TABLE IF NOT EXISTS goals (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL DEFAULT 'default-user',
            parent_id TEXT,
            name TEXT NOT NULL,
            description TEXT,
            type TEXT NOT NULL CHECK (type IN ('long-term', 'short-term', 'habit')),
            why_power TEXT NOT NULL,
            domains TEXT, -- JSON字符串存储数组
            start_date DATETIME,
            deadline DATETIME,
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
            level INTEGER DEFAULT 0,
            repeat_type TEXT CHECK (repeat_type IN ('daily', 'weekly', 'workdays', 'monthly', 'yearly', 'custom')),
            custom_repeat TEXT, -- JSON字符串存储自定义重复设置
            analysis TEXT, -- JSON字符串存储目标解析结果
            -- AI分解相关字段
            decomposition_status TEXT DEFAULT 'not_started' CHECK (decomposition_status IN ('not_started', 'in_progress', 'completed', 'failed', 'user_modified')),
            current_decomposition_session_id TEXT,
            has_ai_decomposition BOOLEAN DEFAULT FALSE,
            ai_decomposition_history TEXT, -- JSON字符串存储历史会话ID列表
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES goals(id) ON DELETE CASCADE
        );

        -- 子目标表
        CREATE TABLE IF NOT EXISTS sub_goals (
            id TEXT PRIMARY KEY,
            parent_goal_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
            estimated_time INTEGER, -- 小时
            deadline DATETIME,
            status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused', 'cancelled')),
            order_index INTEGER DEFAULT 0,
            is_ai_generated BOOLEAN DEFAULT FALSE,
            ai_confidence REAL, -- 0-1
            user_modified BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_goal_id) REFERENCES goals(id) ON DELETE CASCADE
        );

        -- 里程碑表
        CREATE TABLE IF NOT EXISTS milestones (
            id TEXT PRIMARY KEY,
            sub_goal_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            estimated_time INTEGER, -- 小时
            deadline DATETIME,
            status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused', 'cancelled')),
            order_index INTEGER DEFAULT 0,
            is_ai_generated BOOLEAN DEFAULT FALSE,
            ai_confidence REAL, -- 0-1
            user_modified BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sub_goal_id) REFERENCES sub_goals(id) ON DELETE CASCADE
        );

        -- 任务表（更新）
        CREATE TABLE IF NOT EXISTS tasks (
            id TEXT PRIMARY KEY,
            parent_id TEXT NOT NULL,
            parent_type TEXT NOT NULL CHECK (parent_type IN ('subgoal', 'milestone')),
            title TEXT NOT NULL,
            description TEXT,
            estimated_time INTEGER, -- 分钟，应≤120
            actual_time INTEGER DEFAULT 0, -- 分钟
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
            deadline DATETIME,
            tags TEXT, -- JSON字符串存储数组
            status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in-progress', 'completed', 'paused', 'cancelled')),
            completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
            order_index INTEGER DEFAULT 0,
            is_ai_generated BOOLEAN DEFAULT FALSE,
            ai_confidence REAL, -- 0-1
            user_modified BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- AI分解会话表
        CREATE TABLE IF NOT EXISTS decomposition_sessions (
            id TEXT PRIMARY KEY,
            goal_id TEXT NOT NULL,
            status TEXT DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'failed', 'user_modified')),
            ai_provider TEXT NOT NULL,
            original_input TEXT NOT NULL,
            ai_response TEXT,
            decomposition_result TEXT, -- JSON字符串存储分解结果
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (goal_id) REFERENCES goals(id) ON DELETE CASCADE
        );

        -- 用户修改记录表
        CREATE TABLE IF NOT EXISTS user_modifications (
            id TEXT PRIMARY KEY,
            session_id TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('add', 'edit', 'delete', 'reorder')),
            target_type TEXT NOT NULL CHECK (target_type IN ('subgoal', 'milestone', 'task')),
            target_id TEXT NOT NULL,
            old_value TEXT, -- JSON字符串
            new_value TEXT, -- JSON字符串
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES decomposition_sessions(id) ON DELETE CASCADE
        );

        -- 番茄钟会话表
        CREATE TABLE IF NOT EXISTS pomodoro_sessions (
            id TEXT PRIMARY KEY,
            task_id TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('work', 'short-break', 'long-break')),
            start_time DATETIME NOT NULL,
            end_time DATETIME,
            duration INTEGER NOT NULL, -- 分钟
            is_completed BOOLEAN DEFAULT FALSE,
            was_interrupted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
        );

        -- 设置表
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- AI提供商表
        CREATE TABLE IF NOT EXISTS ai_providers (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            base_url TEXT NOT NULL,
            api_key TEXT NOT NULL,
            model_id TEXT NOT NULL,
            enabled BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 创建基础索引
        CREATE INDEX IF NOT EXISTS idx_goals_user_id ON goals(user_id);
        CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status);
        CREATE INDEX IF NOT EXISTS idx_goals_type ON goals(type);
        CREATE INDEX IF NOT EXISTS idx_goals_parent_id ON goals(parent_id);
        CREATE INDEX IF NOT EXISTS idx_pomodoro_sessions_task_id ON pomodoro_sessions(task_id);
        CREATE INDEX IF NOT EXISTS idx_ai_providers_enabled ON ai_providers(enabled);

        -- 插入默认设置
        INSERT OR IGNORE INTO settings (key, value) VALUES 
        ('pomodoro_work_duration', '25'),
        ('pomodoro_short_break_duration', '5'),
        ('pomodoro_long_break_duration', '15'),
        ('pomodoro_sessions_until_long_break', '4'),
        ('pomodoro_auto_start_breaks', 'false'),
        ('pomodoro_auto_start_next_session', 'false'),
        ('theme', 'light'),
        ('language', 'zh-CN'),
        ('notifications_enabled', 'true'),
        ('auto_start', 'false');
      `;
      
      this.db.exec(schema);

      // 检查并添加分解会话版本管理字段
      this.addDecompositionSessionVersionFields();

      console.log('数据库表创建成功');
    } catch (error) {
      console.error('创建数据库表失败:', error);
      throw error;
    }
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  public close() {
    this.db.close();
  }

  // 目标相关操作
  public createGoal(goal: any) {
    const stmt = this.db.prepare(`
      INSERT INTO goals (
        id, user_id, parent_id, name, description, type, why_power, domains,
        start_date, deadline, status, level, repeat_type, custom_repeat, analysis,
        decomposition_status, current_decomposition_session_id, has_ai_decomposition,
        ai_decomposition_history, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      goal.id,
      goal.userId || 'default-user',
      goal.parentId || null,
      goal.name,
      goal.description,
      goal.type,
      goal.whyPower,
      JSON.stringify(goal.domains || []),
      goal.startDate,
      goal.deadline,
      goal.status || 'active',
      goal.level || 0,
      goal.repeatType,
      goal.customRepeat ? JSON.stringify(goal.customRepeat) : null,
      goal.analysis ? JSON.stringify(goal.analysis) : null,
      goal.decompositionStatus || 'not_started',
      goal.currentDecompositionSessionId || null,
      goal.hasAIDecomposition ? 1 : 0,
      goal.aiDecompositionHistory ? JSON.stringify(goal.aiDecompositionHistory) : null,
      goal.createdAt,
      goal.updatedAt
    );
  }

  public updateGoal(id: string, updates: any) {
    const fields = [];
    const values = [];
    
    if (updates.name) {
      fields.push('name = ?');
      values.push(updates.name);
    }
    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }
    if (updates.type) {
      fields.push('type = ?');
      values.push(updates.type);
    }
    if (updates.whyPower) {
      fields.push('why_power = ?');
      values.push(updates.whyPower);
    }
    if (updates.domains) {
      fields.push('domains = ?');
      values.push(JSON.stringify(updates.domains));
    }
    if (updates.startDate !== undefined) {
      fields.push('start_date = ?');
      values.push(updates.startDate);
    }
    if (updates.deadline !== undefined) {
      fields.push('deadline = ?');
      values.push(updates.deadline);
    }
    if (updates.status) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.repeatType !== undefined) {
      fields.push('repeat_type = ?');
      values.push(updates.repeatType);
    }
    if (updates.customRepeat !== undefined) {
      fields.push('custom_repeat = ?');
      values.push(updates.customRepeat ? JSON.stringify(updates.customRepeat) : null);
    }
    if (updates.analysis !== undefined) {
      fields.push('analysis = ?');
      values.push(updates.analysis ? JSON.stringify(updates.analysis) : null);
    }
    if (updates.decompositionStatus) {
      fields.push('decomposition_status = ?');
      values.push(updates.decompositionStatus);
    }
    if (updates.currentDecompositionSessionId !== undefined) {
      fields.push('current_decomposition_session_id = ?');
      values.push(updates.currentDecompositionSessionId);
    }
    if (updates.hasAIDecomposition !== undefined) {
      fields.push('has_ai_decomposition = ?');
      values.push(updates.hasAIDecomposition ? 1 : 0);
    }
    if (updates.aiDecompositionHistory !== undefined) {
      fields.push('ai_decomposition_history = ?');
      values.push(updates.aiDecompositionHistory ? JSON.stringify(updates.aiDecompositionHistory) : null);
    }

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE goals SET ${fields.join(', ')} WHERE id = ?
    `);
    
    return stmt.run(...values);
  }

  public deleteGoal(id: string) {
    const stmt = this.db.prepare('DELETE FROM goals WHERE id = ?');
    return stmt.run(id);
  }

  // 级联删除相关方法

  public getGoals() {
    const stmt = this.db.prepare(`
      SELECT
        id,
        user_id as userId,
        parent_id as parentId,
        name,
        description,
        type,
        why_power as whyPower,
        domains,
        start_date as startDate,
        deadline,
        status,
        level,
        repeat_type as repeatType,
        custom_repeat as customRepeat,
        analysis,
        decomposition_status as decompositionStatus,
        current_decomposition_session_id as currentDecompositionSessionId,
        has_ai_decomposition as hasAIDecomposition,
        ai_decomposition_history as aiDecompositionHistory,
        created_at as createdAt,
        updated_at as updatedAt
      FROM goals
      ORDER BY created_at DESC
    `);

    const goals = stmt.all();
    return goals.map((goal: any) => ({
      ...goal,
      domains: goal.domains ? JSON.parse(goal.domains) : [],
      startDate: goal.startDate ? new Date(goal.startDate) : null,
      deadline: goal.deadline ? new Date(goal.deadline) : null,
      customRepeat: goal.customRepeat ? JSON.parse(goal.customRepeat) : null,
      analysis: goal.analysis ? JSON.parse(goal.analysis) : null,
      hasAIDecomposition: Boolean(goal.hasAIDecomposition),
      aiDecompositionHistory: goal.aiDecompositionHistory ? JSON.parse(goal.aiDecompositionHistory) : [],
      createdAt: new Date(goal.createdAt),
      updatedAt: new Date(goal.updatedAt)
    }));
  }

  public getGoalById(id: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        user_id as userId,
        parent_id as parentId,
        name,
        description,
        type,
        why_power as whyPower,
        domains,
        start_date as startDate,
        deadline,
        status,
        level,
        repeat_type as repeatType,
        custom_repeat as customRepeat,
        analysis,
        decomposition_status as decompositionStatus,
        current_decomposition_session_id as currentDecompositionSessionId,
        has_ai_decomposition as hasAIDecomposition,
        ai_decomposition_history as aiDecompositionHistory,
        created_at as createdAt,
        updated_at as updatedAt
      FROM goals
      WHERE id = ?
    `);

    const goal = stmt.get(id) as any;
    if (!goal) return null;

    return {
      ...goal,
      domains: goal.domains ? JSON.parse(goal.domains) : [],
      startDate: goal.startDate ? new Date(goal.startDate) : null,
      deadline: goal.deadline ? new Date(goal.deadline) : null,
      customRepeat: goal.customRepeat ? JSON.parse(goal.customRepeat) : null,
      analysis: goal.analysis ? JSON.parse(goal.analysis) : null,
      hasAIDecomposition: Boolean(goal.hasAIDecomposition),
      aiDecompositionHistory: goal.aiDecompositionHistory ? JSON.parse(goal.aiDecompositionHistory) : [],
      createdAt: new Date(goal.createdAt),
      updatedAt: new Date(goal.updatedAt)
    };
  }

  // 任务相关操作
  public createTask(task: any) {
    // 检查表结构，确定使用哪种插入语句
    const hasGoalId = this.checkColumnsExist('tasks', ['goal_id']).goal_id;

    if (hasGoalId) {
      // 旧表结构
      const stmt = this.db.prepare(`
        INSERT INTO tasks (id, goal_id, title, description, estimated_time, actual_time, priority, deadline, tags, status, completion_percentage, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      return stmt.run(
        task.id,
        task.goalNodeId || task.goalId,
        task.title,
        task.description,
        task.estimatedTime,
        task.actualTime || 0,
        task.priority || 'medium',
        task.deadline,
        JSON.stringify(task.tags || []),
        task.status || 'todo',
        task.completionPercentage || 0,
        task.createdAt,
        task.updatedAt
      );
    } else {
      // 新表结构
      const stmt = this.db.prepare(`
        INSERT INTO tasks (
          id, parent_id, parent_type, title, description, estimated_time, 
          priority, tags, status, order_index, is_ai_generated, ai_confidence,
          user_modified, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      return stmt.run(
        task.id,
        task.parentId,
        task.parentType,
        task.title,
        task.description,
        task.estimatedTime || null,
        task.priority || 'medium',
        JSON.stringify(task.resources || []),
        task.status || 'todo',
        task.order || 0,
        task.isAIGenerated ? 1 : 0,
        task.aiConfidence || null,
        task.userModified ? 1 : 0,
        task.createdAt || new Date().toISOString(),
        task.updatedAt || new Date().toISOString()
      );
    }
  }

  public updateTask(id: string, updates: any) {
    const fields = [];
    const values = [];
    
    if (updates.title) {
      fields.push('title = ?');
      values.push(updates.title);
    }
    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }
    if (updates.estimatedTime !== undefined) {
      fields.push('estimated_time = ?');
      values.push(updates.estimatedTime);
    }
    if (updates.actualTime !== undefined) {
      fields.push('actual_time = ?');
      values.push(updates.actualTime);
    }
    if (updates.priority) {
      fields.push('priority = ?');
      values.push(updates.priority);
    }
    if (updates.deadline !== undefined) {
      fields.push('deadline = ?');
      values.push(updates.deadline);
    }
    if (updates.tags) {
      fields.push('tags = ?');
      values.push(JSON.stringify(updates.tags));
    }
    if (updates.status) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.completionPercentage !== undefined) {
      fields.push('completion_percentage = ?');
      values.push(updates.completionPercentage);
    }
    
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE tasks SET ${fields.join(', ')} WHERE id = ?
    `);
    
    return stmt.run(...values);
  }

  public deleteTask(id: string) {
    const stmt = this.db.prepare('DELETE FROM tasks WHERE id = ?');
    return stmt.run(id);
  }

  public getTasks() {
    // 首先检查表结构，确定使用哪种查询
    const hasGoalId = this.checkColumnsExist('tasks', ['goal_id']).goal_id;
    console.log('数据库getTasks - 表结构检查:', { hasGoalId });

    let stmt;
    if (hasGoalId) {
      // 旧表结构
      stmt = this.db.prepare(`
        SELECT
          id,
          goal_id as goalNodeId,
          title,
          description,
          estimated_time as estimatedTime,
          actual_time as actualTime,
          priority,
          deadline,
          tags,
          status,
          completion_percentage as completionPercentage,
          created_at as createdAt,
          updated_at as updatedAt
        FROM tasks
        ORDER BY created_at DESC
      `);
    } else {
      // 新表结构
      stmt = this.db.prepare(`
        SELECT
          id,
          parent_id as parentId,
          parent_type as parentType,
          title,
          description,
          estimated_time as estimatedTime,
          priority,
          ai_confidence as confidence,
          tags,
          is_ai_generated as isAIGenerated,
          ai_confidence as aiConfidence,
          user_modified as userModified,
          order_index as orderIndex,
          status,
          created_at as createdAt,
          updated_at as updatedAt
        FROM tasks
        ORDER BY order_index ASC, created_at DESC
      `);
    }

    const tasks = stmt.all();
    console.log('数据库getTasks - 原始查询结果:', tasks);
    
    const mappedTasks = tasks.map((task: any) => ({
      ...task,
      tags: task.tags ? JSON.parse(task.tags) : [],
      resources: task.resources ? JSON.parse(task.resources) : [],
      deadline: task.deadline ? new Date(task.deadline) : null,
      actionable: task.actionable ? Boolean(task.actionable) : true,
      isAIGenerated: task.isAIGenerated ? Boolean(task.isAIGenerated) : false,
      userModified: task.userModified ? Boolean(task.userModified) : false,
      createdAt: new Date(task.createdAt),
      updatedAt: new Date(task.updatedAt)
    }));
    
    console.log('数据库getTasks - 映射后结果:', mappedTasks);
    return mappedTasks;
  }

  // 番茄钟会话相关操作
  public createPomodoroSession(session: any) {
    // 验证和转换参数类型，确保符合SQLite要求
    const validatedSession = this.validatePomodoroSessionData(session);

    const stmt = this.db.prepare(`
      INSERT INTO pomodoro_sessions (id, task_id, type, start_time, end_time, duration, is_completed, was_interrupted)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      validatedSession.id,
      validatedSession.taskId,
      validatedSession.type,
      validatedSession.startTime,
      validatedSession.endTime,
      validatedSession.duration,
      validatedSession.isCompleted,
      validatedSession.wasInterrupted
    );
  }

  /**
   * 验证和转换番茄钟会话数据
   * 确保所有参数都是SQLite支持的数据类型
   */
  private validatePomodoroSessionData(session: any) {
    if (!session) {
      throw new Error('番茄钟会话数据不能为空');
    }

    // 验证必需字段
    if (!session.id || typeof session.id !== 'string') {
      throw new Error('番茄钟会话ID必须是字符串');
    }
    if (!session.taskId || typeof session.taskId !== 'string') {
      throw new Error('任务ID必须是字符串');
    }
    if (!session.type || !['work', 'short-break', 'long-break'].includes(session.type)) {
      throw new Error('番茄钟类型必须是 work、short-break 或 long-break');
    }
    if (!session.startTime) {
      throw new Error('开始时间不能为空');
    }
    if (typeof session.duration !== 'number' || session.duration <= 0) {
      throw new Error('持续时间必须是正数');
    }

    return {
      id: session.id,
      taskId: session.taskId,
      type: session.type,
      // 将Date对象转换为ISO字符串
      startTime: session.startTime instanceof Date
        ? session.startTime.toISOString()
        : (typeof session.startTime === 'string' ? session.startTime : new Date(session.startTime).toISOString()),
      // endTime可能为空，需要特殊处理
      endTime: session.endTime
        ? (session.endTime instanceof Date
          ? session.endTime.toISOString()
          : (typeof session.endTime === 'string' ? session.endTime : new Date(session.endTime).toISOString()))
        : null,
      duration: Number(session.duration),
      // 布尔值转换为数字（SQLite中布尔值存储为0/1）
      isCompleted: session.isCompleted ? 1 : 0,
      wasInterrupted: session.wasInterrupted ? 1 : 0
    };
  }

  public updatePomodoroSession(id: string, updates: any) {
    if (!id || typeof id !== 'string') {
      throw new Error('番茄钟会话ID必须是字符串');
    }

    const fields = [];
    const values = [];

    if (updates.endTime !== undefined) {
      fields.push('end_time = ?');
      // 安全地转换endTime
      if (updates.endTime === null) {
        values.push(null);
      } else if (updates.endTime instanceof Date) {
        values.push(updates.endTime.toISOString());
      } else if (typeof updates.endTime === 'string') {
        values.push(updates.endTime);
      } else {
        values.push(new Date(updates.endTime).toISOString());
      }
    }
    if (updates.duration !== undefined) {
      fields.push('duration = ?');
      values.push(Number(updates.duration));
    }
    if (updates.isCompleted !== undefined) {
      fields.push('is_completed = ?');
      values.push(updates.isCompleted ? 1 : 0);
    }
    if (updates.wasInterrupted !== undefined) {
      fields.push('was_interrupted = ?');
      values.push(updates.wasInterrupted ? 1 : 0);
    }

    if (fields.length === 0) {
      throw new Error('没有提供更新字段');
    }

    const stmt = this.db.prepare(`
      UPDATE pomodoro_sessions
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    values.push(id);
    return stmt.run(...values);
  }

  public getPomodoroSessions() {
    const stmt = this.db.prepare(`
      SELECT 
        id,
        task_id as taskId,
        type,
        start_time as startTime,
        end_time as endTime,
        duration,
        is_completed as isCompleted,
        was_interrupted as wasInterrupted,
        created_at as createdAt
      FROM pomodoro_sessions 
      ORDER BY created_at DESC
    `);
    
    const sessions = stmt.all();
    return sessions.map((session: any) => ({
      ...session,
      startTime: new Date(session.startTime),
      endTime: session.endTime ? new Date(session.endTime) : null,
      createdAt: new Date(session.createdAt)
    }));
  }

  // 设置相关操作
  public getSetting(key: string) {
    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?');
    const result = stmt.get(key) as any;
    return result ? result.value : null;
  }

  public setSetting(key: string, value: string) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at) 
      VALUES (?, ?, ?)
    `);
    return stmt.run(key, value, new Date().toISOString());
  }

  public getAllSettings() {
    const stmt = this.db.prepare('SELECT key, value FROM settings');
    const settings = stmt.all();
    const result: { [key: string]: string } = {};
    settings.forEach((setting: any) => {
      result[setting.key] = setting.value;
    });
    return result;
  }

  // 数据库迁移方法
  private runMigrations() {
    try {
      // 检查是否需要为 goals 表添加新字段
      const goalFields = this.checkColumnsExist('goals', [
        'repeat_type', 'custom_repeat', 'analysis',
        'decomposition_status', 'current_decomposition_session_id',
        'has_ai_decomposition', 'ai_decomposition_history'
      ]);

      if (!goalFields.repeat_type) {
        console.log('添加 repeat_type 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN repeat_type TEXT CHECK (repeat_type IN ('daily', 'weekly', 'workdays', 'monthly', 'yearly', 'custom'));
        `);
      }

      if (!goalFields.custom_repeat) {
        console.log('添加 custom_repeat 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN custom_repeat TEXT;
        `);
      }

      if (!goalFields.analysis) {
        console.log('添加 analysis 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN analysis TEXT;
        `);
      }

      // 添加AI分解相关字段
      if (!goalFields.decomposition_status) {
        console.log('添加 decomposition_status 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN decomposition_status TEXT DEFAULT 'not_started' CHECK (decomposition_status IN ('not_started', 'in_progress', 'completed', 'failed', 'user_modified'));
        `);
      }

      if (!goalFields.current_decomposition_session_id) {
        console.log('添加 current_decomposition_session_id 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN current_decomposition_session_id TEXT;
        `);
      }

      if (!goalFields.has_ai_decomposition) {
        console.log('添加 has_ai_decomposition 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN has_ai_decomposition BOOLEAN DEFAULT FALSE;
        `);
      }

      if (!goalFields.ai_decomposition_history) {
        console.log('添加 ai_decomposition_history 字段到 goals 表');
        this.db.exec(`
          ALTER TABLE goals ADD COLUMN ai_decomposition_history TEXT;
        `);
      }

      // 检查是否需要创建 ai_providers 表
      const tableExists = this.checkTableExists('ai_providers');
      if (!tableExists) {
        console.log('创建 ai_providers 表');
        this.db.exec(`
          CREATE TABLE ai_providers (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            base_url TEXT NOT NULL,
            api_key TEXT NOT NULL,
            model_id TEXT NOT NULL,
            enabled BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          );
          CREATE INDEX idx_ai_providers_enabled ON ai_providers(enabled);
        `);
      } else {
        // 检查现有表结构是否需要更新
        console.log('检查 ai_providers 表结构');

        try {
          // 尝试查询新字段，如果失败说明需要重建表
          this.db.prepare('SELECT model_id FROM ai_providers LIMIT 1').all();
          console.log('ai_providers 表结构已是最新');
        } catch (error: any) {
          if (error.code === 'SQLITE_ERROR' && error.message.includes('no such column: model_id')) {
            console.log('重建 ai_providers 表以更新结构');

            try {
              // 备份现有数据（只保留兼容的字段）
              this.db.exec(`
                CREATE TABLE ai_providers_backup AS
                SELECT id, name, base_url, api_key, enabled, created_at, updated_at
                FROM ai_providers;
              `);

              // 删除旧表
              this.db.exec(`DROP TABLE ai_providers;`);

              // 创建新表
              this.db.exec(`
                CREATE TABLE ai_providers (
                  id TEXT PRIMARY KEY,
                  name TEXT NOT NULL,
                  base_url TEXT NOT NULL,
                  api_key TEXT NOT NULL,
                  model_id TEXT NOT NULL DEFAULT 'gpt-3.5-turbo',
                  enabled BOOLEAN DEFAULT TRUE,
                  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
              `);

              // 恢复数据，为model_id设置默认值
              this.db.exec(`
                INSERT INTO ai_providers (id, name, base_url, api_key, model_id, enabled, created_at, updated_at)
                SELECT id, name, base_url, api_key, 'gpt-3.5-turbo', enabled, created_at, updated_at
                FROM ai_providers_backup;
              `);

              // 删除备份表
              this.db.exec(`DROP TABLE ai_providers_backup;`);

              // 创建索引
              this.db.exec(`CREATE INDEX idx_ai_providers_enabled ON ai_providers(enabled);`);

              console.log('ai_providers 表重建完成');
            } catch (rebuildError) {
              console.error('重建 ai_providers 表失败:', rebuildError);
              throw rebuildError;
            }
          } else {
            throw error;
          }
        }
      }

      // 检查并创建新表
      this.createNewTablesIfNotExist();

      // 检查并更新tasks表结构
      this.migrateTasksTable();

    } catch (error) {
      console.error('运行数据库迁移失败:', error);
      throw error;
    }
  }

  // 创建新表（如果不存在）
  private createNewTablesIfNotExist() {
    // 创建sub_goals表
    if (!this.checkTableExists('sub_goals')) {
      console.log('创建 sub_goals 表');
      this.db.exec(`
        CREATE TABLE sub_goals (
          id TEXT PRIMARY KEY,
          parent_goal_id TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
          estimated_time INTEGER,
          deadline DATETIME,
          status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused', 'cancelled')),
          order_index INTEGER DEFAULT 0,
          is_ai_generated BOOLEAN DEFAULT FALSE,
          ai_confidence REAL,
          user_modified BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (parent_goal_id) REFERENCES goals(id) ON DELETE CASCADE
        );
        CREATE INDEX idx_sub_goals_parent_goal_id ON sub_goals(parent_goal_id);
        CREATE INDEX idx_sub_goals_status ON sub_goals(status);
        CREATE INDEX idx_sub_goals_order ON sub_goals(order_index);
      `);
    }

    // 创建milestones表
    if (!this.checkTableExists('milestones')) {
      console.log('创建 milestones 表');
      this.db.exec(`
        CREATE TABLE milestones (
          id TEXT PRIMARY KEY,
          sub_goal_id TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          estimated_time INTEGER,
          deadline DATETIME,
          status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused', 'cancelled')),
          order_index INTEGER DEFAULT 0,
          is_ai_generated BOOLEAN DEFAULT FALSE,
          ai_confidence REAL,
          user_modified BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sub_goal_id) REFERENCES sub_goals(id) ON DELETE CASCADE
        );
        CREATE INDEX idx_milestones_sub_goal_id ON milestones(sub_goal_id);
        CREATE INDEX idx_milestones_status ON milestones(status);
        CREATE INDEX idx_milestones_order ON milestones(order_index);
      `);
    }

    // 创建decomposition_sessions表
    if (!this.checkTableExists('decomposition_sessions')) {
      console.log('创建 decomposition_sessions 表');
      this.db.exec(`
        CREATE TABLE decomposition_sessions (
          id TEXT PRIMARY KEY,
          goal_id TEXT NOT NULL,
          status TEXT DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'failed', 'user_modified')),
          ai_provider TEXT NOT NULL,
          original_input TEXT NOT NULL,
          ai_response TEXT,
          decomposition_result TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (goal_id) REFERENCES goals(id) ON DELETE CASCADE
        );
        CREATE INDEX idx_decomposition_sessions_goal_id ON decomposition_sessions(goal_id);
        CREATE INDEX idx_decomposition_sessions_status ON decomposition_sessions(status);
      `);
    }

    // 创建user_modifications表
    if (!this.checkTableExists('user_modifications')) {
      console.log('创建 user_modifications 表');
      this.db.exec(`
        CREATE TABLE user_modifications (
          id TEXT PRIMARY KEY,
          session_id TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('add', 'edit', 'delete', 'reorder')),
          target_type TEXT NOT NULL CHECK (target_type IN ('subgoal', 'milestone', 'task')),
          target_id TEXT NOT NULL,
          old_value TEXT,
          new_value TEXT,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES decomposition_sessions(id) ON DELETE CASCADE
        );
        CREATE INDEX idx_user_modifications_session_id ON user_modifications(session_id);
        CREATE INDEX idx_user_modifications_target ON user_modifications(target_type, target_id);
      `);
    }
  }

  // 迁移tasks表结构
  private migrateTasksTable() {
    const taskFields = this.checkColumnsExist('tasks', [
      'parent_id', 'parent_type', 'order_index',
      'is_ai_generated', 'ai_confidence', 'user_modified'
    ]);

    // 如果缺少新字段，需要重建tasks表
    if (!taskFields.parent_id || !taskFields.parent_type) {
      console.log('重建 tasks 表以支持新的层级结构');

      try {
        // 备份现有数据
        this.db.exec(`
          CREATE TABLE tasks_backup AS
          SELECT id, goal_id, title, description, estimated_time, actual_time,
                 priority, deadline, tags, status, completion_percentage,
                 created_at, updated_at
          FROM tasks;
        `);

        // 删除旧表
        this.db.exec(`DROP TABLE tasks;`);

        // 创建新表
        this.db.exec(`
          CREATE TABLE tasks (
            id TEXT PRIMARY KEY,
            parent_id TEXT NOT NULL,
            parent_type TEXT NOT NULL CHECK (parent_type IN ('subgoal', 'milestone')),
            title TEXT NOT NULL,
            description TEXT,
            estimated_time INTEGER,
            actual_time INTEGER DEFAULT 0,
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
            deadline DATETIME,
            tags TEXT,
            status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in-progress', 'completed', 'paused', 'cancelled')),
            completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
            order_index INTEGER DEFAULT 0,
            is_ai_generated BOOLEAN DEFAULT FALSE,
            ai_confidence REAL,
            user_modified BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);

        // 注意：由于表结构变化较大，现有任务数据可能无法直接迁移
        // 这里我们保留备份表，让用户手动处理
        console.log('tasks表重建完成，原数据保存在tasks_backup表中');

        // 创建索引
        this.db.exec(`
          CREATE INDEX idx_tasks_parent_id ON tasks(parent_id);
          CREATE INDEX idx_tasks_parent_type ON tasks(parent_type);
          CREATE INDEX idx_tasks_status ON tasks(status);
          CREATE INDEX idx_tasks_deadline ON tasks(deadline);
          CREATE INDEX idx_tasks_order ON tasks(order_index);
        `);

      } catch (error) {
        console.error('重建tasks表失败:', error);
        throw error;
      }
    } else {
      // 只需要添加缺少的字段
      if (!taskFields.order_index) {
        console.log('添加 order_index 字段到 tasks 表');
        this.db.exec(`ALTER TABLE tasks ADD COLUMN order_index INTEGER DEFAULT 0;`);
      }
      if (!taskFields.is_ai_generated) {
        console.log('添加 is_ai_generated 字段到 tasks 表');
        this.db.exec(`ALTER TABLE tasks ADD COLUMN is_ai_generated BOOLEAN DEFAULT FALSE;`);
      }
      if (!taskFields.ai_confidence) {
        console.log('添加 ai_confidence 字段到 tasks 表');
        this.db.exec(`ALTER TABLE tasks ADD COLUMN ai_confidence REAL;`);
      }
      if (!taskFields.user_modified) {
        console.log('添加 user_modified 字段到 tasks 表');
        this.db.exec(`ALTER TABLE tasks ADD COLUMN user_modified BOOLEAN DEFAULT FALSE;`);
      }
    }
  }

  // 添加分解会话版本管理字段
  private addDecompositionSessionVersionFields() {
    try {
      const sessionFields = this.checkColumnsExist('decomposition_sessions', [
        'version', 'is_active', 'replaced_by', 'replacement_reason', 'applied_at'
      ]);

      if (!sessionFields.version) {
        console.log('添加 version 字段到 decomposition_sessions 表');
        this.db.exec(`ALTER TABLE decomposition_sessions ADD COLUMN version INTEGER DEFAULT 1;`);
      }
      if (!sessionFields.is_active) {
        console.log('添加 is_active 字段到 decomposition_sessions 表');
        this.db.exec(`ALTER TABLE decomposition_sessions ADD COLUMN is_active BOOLEAN DEFAULT 1;`);
      }
      if (!sessionFields.replaced_by) {
        console.log('添加 replaced_by 字段到 decomposition_sessions 表');
        this.db.exec(`ALTER TABLE decomposition_sessions ADD COLUMN replaced_by TEXT;`);
      }
      if (!sessionFields.replacement_reason) {
        console.log('添加 replacement_reason 字段到 decomposition_sessions 表');
        this.db.exec(`ALTER TABLE decomposition_sessions ADD COLUMN replacement_reason TEXT;`);
      }
      if (!sessionFields.applied_at) {
        console.log('添加 applied_at 字段到 decomposition_sessions 表');
        this.db.exec(`ALTER TABLE decomposition_sessions ADD COLUMN applied_at DATETIME;`);
      }
    } catch (error) {
      console.error('添加分解会话版本管理字段失败:', error);
    }
  }

  // 检查表是否存在
  private checkTableExists(tableName: string): boolean {
    try {
      const result = this.db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name=?
      `).get(tableName);
      return !!result;
    } catch (error) {
      console.error(`检查表 ${tableName} 是否存在失败:`, error);
      return false;
    }
  }

  // 检查表中是否存在指定列
  private checkColumnsExist(tableName: string, columnNames: string[]): Record<string, boolean> {
    const result: Record<string, boolean> = {};
    
    try {
      const columns = this.db.prepare(`PRAGMA table_info(${tableName})`).all() as any[];
      const existingColumns = columns.map(col => col.name);
      
      columnNames.forEach(columnName => {
        result[columnName] = existingColumns.includes(columnName);
      });
    } catch (error) {
      console.error(`检查表 ${tableName} 列失败:`, error);
      columnNames.forEach(columnName => {
        result[columnName] = false;
      });
    }
    
    return result;
  }

  // AI Provider 相关操作
  public createAIProvider(provider: any) {
    const stmt = this.db.prepare(`
      INSERT INTO ai_providers (id, name, base_url, api_key, model_id, enabled, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      provider.id,
      provider.name,
      provider.baseUrl,
      provider.apiKey,
      provider.modelId,
      provider.enabled ? 1 : 0,
      provider.createdAt ? provider.createdAt.toISOString() : new Date().toISOString(),
      provider.updatedAt ? provider.updatedAt.toISOString() : new Date().toISOString()
    );
  }

  public updateAIProvider(id: string, updates: any) {
    const fields = [];
    const values = [];

    if (updates.name) {
      fields.push('name = ?');
      values.push(updates.name);
    }
    if (updates.baseUrl) {
      fields.push('base_url = ?');
      values.push(updates.baseUrl);
    }
    if (updates.apiKey) {
      fields.push('api_key = ?');
      values.push(updates.apiKey);
    }
    if (updates.modelId) {
      fields.push('model_id = ?');
      values.push(updates.modelId);
    }
    if (updates.enabled !== undefined) {
      fields.push('enabled = ?');
      values.push(updates.enabled ? 1 : 0);
    }

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(id);

    const stmt = this.db.prepare(`
      UPDATE ai_providers SET ${fields.join(', ')} WHERE id = ?
    `);

    return stmt.run(...values);
  }

  public deleteAIProvider(id: string) {
    const stmt = this.db.prepare('DELETE FROM ai_providers WHERE id = ?');
    return stmt.run(id);
  }

  public getAIProviders() {
    const stmt = this.db.prepare(`
      SELECT
        id,
        name,
        base_url as baseUrl,
        api_key as apiKey,
        model_id as modelId,
        enabled,
        created_at as createdAt,
        updated_at as updatedAt
      FROM ai_providers
      ORDER BY created_at DESC
    `);

    const providers = stmt.all();
    return providers.map((provider: any) => ({
      ...provider,
      enabled: Boolean(provider.enabled),
      createdAt: new Date(provider.createdAt),
      updatedAt: new Date(provider.updatedAt)
    }));
  }

  public getAIProviderById(id: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        name,
        base_url as baseUrl,
        api_key as apiKey,
        model_id as modelId,
        enabled,
        created_at as createdAt,
        updated_at as updatedAt
      FROM ai_providers
      WHERE id = ?
    `);

    const provider = stmt.get(id) as any;
    if (!provider) return null;

    return {
      ...provider,
      enabled: Boolean(provider.enabled),
      createdAt: new Date(provider.createdAt),
      updatedAt: new Date(provider.updatedAt)
    };
  }

  // SubGoal 相关操作
  public createSubGoal(subGoal: any) {
    const stmt = this.db.prepare(`
      INSERT INTO sub_goals (
        id, parent_goal_id, name, description, priority, estimated_time,
        deadline, status, order_index, is_ai_generated, ai_confidence,
        user_modified, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      subGoal.id,
      subGoal.parentGoalId,
      subGoal.name,
      subGoal.description,
      subGoal.priority || 'medium',
      subGoal.estimatedTime || null,
      subGoal.deadline ? (subGoal.deadline instanceof Date ? subGoal.deadline.toISOString() : subGoal.deadline) : null,
      subGoal.status || 'not_started',
      subGoal.order || 0,
      subGoal.isAIGenerated ? 1 : 0,
      subGoal.aiConfidence || null,
      subGoal.userModified ? 1 : 0,
      subGoal.createdAt || new Date().toISOString(),
      subGoal.updatedAt || new Date().toISOString()
    );
  }

  public getSubGoalsByGoalId(goalId: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        parent_goal_id as parentGoalId,
        name,
        description,
        priority,
        estimated_time as estimatedTime,
        deadline,
        status,
        order_index as orderIndex,
        is_ai_generated as isAIGenerated,
        ai_confidence as aiConfidence,
        user_modified as userModified,
        created_at as createdAt,
        updated_at as updatedAt
      FROM sub_goals
      WHERE parent_goal_id = ?
      ORDER BY order_index ASC, created_at ASC
    `);

    const subGoals = stmt.all(goalId);
    return subGoals.map((subGoal: any) => ({
      ...subGoal,
      deadline: subGoal.deadline ? new Date(subGoal.deadline) : null,
      isAIGenerated: Boolean(subGoal.isAIGenerated),
      userModified: Boolean(subGoal.userModified),
      createdAt: new Date(subGoal.createdAt),
      updatedAt: new Date(subGoal.updatedAt)
    }));
  }

  // Milestone 相关操作
  public createMilestone(milestone: any) {
    const stmt = this.db.prepare(`
      INSERT INTO milestones (
        id, sub_goal_id, name, description, estimated_time, deadline,
        status, order_index, is_ai_generated, ai_confidence, user_modified,
        created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      milestone.id,
      milestone.subGoalId,
      milestone.name,
      milestone.description,
      milestone.estimatedTime || null,
      milestone.deadline ? (milestone.deadline instanceof Date ? milestone.deadline.toISOString() : milestone.deadline) : null,
      milestone.status || 'not_started',
      milestone.order || 0,
      milestone.isAIGenerated ? 1 : 0,
      milestone.aiConfidence || null,
      milestone.userModified ? 1 : 0,
      milestone.createdAt || new Date().toISOString(),
      milestone.updatedAt || new Date().toISOString()
    );
  }

  public getMilestonesBySubGoalId(subGoalId: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        sub_goal_id as subGoalId,
        name,
        description,
        estimated_time as estimatedTime,
        deadline,
        status,
        order_index as orderIndex,
        is_ai_generated as isAIGenerated,
        ai_confidence as aiConfidence,
        user_modified as userModified,
        created_at as createdAt,
        updated_at as updatedAt
      FROM milestones
      WHERE sub_goal_id = ?
      ORDER BY order_index ASC, created_at ASC
    `);

    const milestones = stmt.all(subGoalId);
    return milestones.map((milestone: any) => ({
      ...milestone,
      deadline: milestone.deadline ? new Date(milestone.deadline) : null,
      isAIGenerated: Boolean(milestone.isAIGenerated),
      userModified: Boolean(milestone.userModified),
      createdAt: new Date(milestone.createdAt),
      updatedAt: new Date(milestone.updatedAt)
    }));
  }

  // DecompositionSession 相关操作
  public createDecompositionSession(session: any) {
    const stmt = this.db.prepare(`
      INSERT INTO decomposition_sessions (
        id, goal_id, status, ai_provider, original_input, ai_response,
        decomposition_result, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    return stmt.run(
      session.id,
      session.goalId,
      session.status || 'in_progress',
      session.aiProvider,
      session.originalInput,
      session.aiResponse,
      session.decompositionResult ? JSON.stringify(session.decompositionResult) : null,
      session.createdAt || new Date().toISOString(),
      session.updatedAt || new Date().toISOString()
    );
  }

  public updateDecompositionSession(id: string, updates: any) {
    const fields = [];
    const values = [];

    if (updates.status) {
      // 数据验证和规范化：确保status值在允许的范围内
      const validStatuses = ['not_started', 'in_progress', 'completed', 'failed', 'user_modified'];
      let normalizedStatus = updates.status;

      // 处理历史遗留的 'applied' 状态
      if (updates.status === 'applied') {
        normalizedStatus = 'completed';
        console.log(`状态值规范化: ${updates.status} -> ${normalizedStatus}`);
      }

      if (!validStatuses.includes(normalizedStatus)) {
        console.error(`无效的status值: ${updates.status}, 允许的值: ${validStatuses.join(', ')}`);
        throw new Error(`无效的status值: ${updates.status}，允许的值: ${validStatuses.join(', ')}`);
      }

      fields.push('status = ?');
      values.push(normalizedStatus);
    }
    if (updates.aiResponse !== undefined) {
      fields.push('ai_response = ?');
      values.push(updates.aiResponse);
    }
    if (updates.decompositionResult !== undefined) {
      fields.push('decomposition_result = ?');
      values.push(updates.decompositionResult ? JSON.stringify(updates.decompositionResult) : null);
    }
    if (updates.version !== undefined) {
      fields.push('version = ?');
      values.push(updates.version);
    }
    if (updates.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(updates.is_active ? 1 : 0);
    }
    if (updates.replaced_by !== undefined) {
      fields.push('replaced_by = ?');
      values.push(updates.replaced_by);
    }
    if (updates.replacement_reason !== undefined) {
      fields.push('replacement_reason = ?');
      values.push(updates.replacement_reason);
    }
    if (updates.applied_at !== undefined) {
      fields.push('applied_at = ?');
      values.push(updates.applied_at);
    }

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(id);

    const stmt = this.db.prepare(`
      UPDATE decomposition_sessions SET ${fields.join(', ')} WHERE id = ?
    `);

    return stmt.run(...values);
  }

  public getDecompositionSessionById(id: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        goal_id as goalId,
        status,
        ai_provider as aiProvider,
        original_input as originalInput,
        ai_response as aiResponse,
        decomposition_result as decompositionResult,
        version,
        is_active,
        replaced_by,
        replacement_reason,
        created_at as createdAt,
        updated_at as updatedAt,
        applied_at as appliedAt
      FROM decomposition_sessions
      WHERE id = ?
    `);

    const session = stmt.get(id) as any;
    if (!session) return null;

    return {
      ...session,
      decompositionResult: session.decompositionResult ? JSON.parse(session.decompositionResult) : null,
      version: session.version || 1,
      is_active: Boolean(session.is_active),
      replaced_by: session.replaced_by,
      replacement_reason: session.replacement_reason,
      createdAt: new Date(session.createdAt),
      updatedAt: new Date(session.updatedAt),
      appliedAt: session.appliedAt ? new Date(session.appliedAt) : null
    };
  }

  public getDecompositionSessionsByGoalId(goalId: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        goal_id as goalId,
        status,
        ai_provider as aiProvider,
        original_input as originalInput,
        ai_response as aiResponse,
        decomposition_result as decompositionResult,
        version,
        is_active,
        replaced_by,
        replacement_reason,
        created_at as createdAt,
        updated_at as updatedAt,
        applied_at as appliedAt
      FROM decomposition_sessions
      WHERE goal_id = ?
      ORDER BY created_at DESC
    `);

    const sessions = stmt.all(goalId);
    return sessions.map((session: any) => ({
      ...session,
      decompositionResult: session.decompositionResult ? JSON.parse(session.decompositionResult) : null,
      version: session.version || 1,
      is_active: Boolean(session.is_active),
      replaced_by: session.replaced_by,
      replacement_reason: session.replacement_reason,
      createdAt: new Date(session.createdAt),
      updatedAt: new Date(session.updatedAt),
      appliedAt: session.appliedAt ? new Date(session.appliedAt) : null
    }));
  }

  public getAllDecompositionSessions() {
    const stmt = this.db.prepare(`
      SELECT
        id,
        goal_id as goalId,
        status,
        ai_provider as aiProvider,
        original_input as originalInput,
        ai_response as aiResponse,
        decomposition_result as decompositionResult,
        created_at as createdAt,
        updated_at as updatedAt
      FROM decomposition_sessions
      ORDER BY created_at DESC
    `);

    const sessions = stmt.all();
    return sessions.map((session: any) => ({
      ...session,
      decompositionResult: session.decompositionResult ? JSON.parse(session.decompositionResult) : null,
      createdAt: new Date(session.createdAt),
      updatedAt: new Date(session.updatedAt)
    }));
  }

  public deleteDecompositionSession(id: string) {
    const stmt = this.db.prepare(`DELETE FROM decomposition_sessions WHERE id = ?`);
    return stmt.run(id);
  }

  public deleteSubGoal(id: string) {
    const stmt = this.db.prepare(`DELETE FROM sub_goals WHERE id = ?`);
    return stmt.run(id);
  }

  public deleteMilestone(id: string) {
    const stmt = this.db.prepare(`DELETE FROM milestones WHERE id = ?`);
    return stmt.run(id);
  }



  // 任务管理方法扩展
  public getTasksByParent(parentType: string, parentId: string) {
    const stmt = this.db.prepare(`
      SELECT
        id,
        parent_type as parentType,
        parent_id as parentId,
        title,
        description,
        estimated_time as estimatedTime,
        priority,
        ai_confidence as confidence,
        tags,
        is_ai_generated as isAIGenerated,
        ai_confidence as aiConfidence,
        user_modified as userModified,
        order_index as orderIndex,
        status,
        created_at as createdAt,
        updated_at as updatedAt
      FROM tasks
      WHERE parent_type = ? AND parent_id = ?
      ORDER BY order_index ASC
    `);

    const tasks = stmt.all(parentType, parentId);
    return tasks.map((task: any) => ({
      ...task,
      tags: task.tags ? JSON.parse(task.tags) : [],
      isAIGenerated: Boolean(task.isAIGenerated),
      userModified: Boolean(task.userModified),
      createdAt: new Date(task.createdAt),
      updatedAt: new Date(task.updatedAt)
    }));
  }
}