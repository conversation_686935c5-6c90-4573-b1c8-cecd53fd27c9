import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PomodoroTimer, PomodoroSettings, PomodoroSession } from '../../types';
import {
  loadPomodoroSessions,
  createPomodoroSessionAsync,
  updatePomodoroSessionAsync,
  completePomodoroSessionAsync,
  loadPomodoroSettingsAsync,
  savePomodoroSettingsAsync,
  getPomodoroStatsAsync,
  getRecentPomodoroTaskAsync
} from '../thunks/pomodoroThunks';

interface PomodoroState {
  timer: PomodoroTimer;
  settings: PomodoroSettings;
  sessions: PomodoroSession[];
  loading: boolean;
  recentTask: {
    taskId: string;
    sessionId: string;
    startTime: Date;
  } | null;
  stats: {
    totalSessions: number;
    completedSessions: number;
    totalFocusTime: number;
    averageSessionDuration: number;
    interruptionRate: number;
    dailyStats: Record<string, number>;
    weeklyStats: Record<string, number>;
  };
}

const initialState: PomodoroState = {
  timer: {
    remainingTime: 25 * 60, // 25分钟，以秒为单位
    isRunning: false,
    isPaused: false,
    sessionCount: 0,
  },
  settings: {
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    sessionsUntilLongBreak: 4,
    autoStartBreaks: false,
    autoStartNextSession: false,
  },
  sessions: [],
  loading: false,
  recentTask: null,
  stats: {
    totalSessions: 0,
    completedSessions: 0,
    totalFocusTime: 0,
    averageSessionDuration: 0,
    interruptionRate: 0,
    dailyStats: {},
    weeklyStats: {},
  },
};

const pomodoroSlice = createSlice({
  name: 'pomodoro',
  initialState,
  reducers: {
    startTimer: (state) => {
      state.timer.isRunning = true;
      state.timer.isPaused = false;
    },
    pauseTimer: (state) => {
      state.timer.isRunning = false;
      state.timer.isPaused = true;
    },
    stopTimer: (state) => {
      state.timer.isRunning = false;
      state.timer.isPaused = false;
      state.timer.remainingTime = state.settings.workDuration * 60;
    },
    setRemainingTime: (state, action: PayloadAction<number>) => {
      state.timer.remainingTime = action.payload;
    },
    setCurrentSession: (state, action: PayloadAction<PomodoroSession | undefined>) => {
      state.timer.currentSession = action.payload;
    },
    incrementSessionCount: (state) => {
      state.timer.sessionCount += 1;
    },
    updateSettings: (state, action: PayloadAction<Partial<PomodoroSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    addSession: (state, action: PayloadAction<PomodoroSession>) => {
      state.sessions.push(action.payload);
    },
    resetTimer: (state) => {
      state.timer.isRunning = false;
      state.timer.isPaused = false;
      state.timer.remainingTime = state.settings.workDuration * 60;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // 加载番茄钟会话
    builder
      .addCase(loadPomodoroSessions.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadPomodoroSessions.fulfilled, (state, action) => {
        state.loading = false;
        state.sessions = action.payload;
      })
      .addCase(loadPomodoroSessions.rejected, (state) => {
        state.loading = false;
      })
      // 创建番茄钟会话
      .addCase(createPomodoroSessionAsync.fulfilled, (state, action) => {
        state.sessions.unshift(action.payload);
      })
      // 更新番茄钟会话
      .addCase(updatePomodoroSessionAsync.fulfilled, (state, action) => {
        const index = state.sessions.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.sessions[index] = action.payload;
        }
      })
      // 完成番茄钟会话
      .addCase(completePomodoroSessionAsync.fulfilled, (state, action) => {
        const index = state.sessions.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.sessions[index] = action.payload;
        }
        // 增加会话计数
        if (action.payload.type === 'work' && action.payload.isCompleted) {
          state.timer.sessionCount += 1;
        }
      })
      // 加载设置
      .addCase(loadPomodoroSettingsAsync.fulfilled, (state, action) => {
        state.settings = action.payload;
        // 更新计时器时间
        if (!state.timer.isRunning && !state.timer.isPaused) {
          state.timer.remainingTime = action.payload.workDuration * 60;
        }
      })
      // 保存设置
      .addCase(savePomodoroSettingsAsync.fulfilled, (state, action) => {
        state.settings = { ...state.settings, ...action.payload };
        // 更新计时器时间
        if (!state.timer.isRunning && !state.timer.isPaused) {
          state.timer.remainingTime = action.payload.workDuration * 60;
        }
      })
      // 统计数据
      .addCase(getPomodoroStatsAsync.fulfilled, (state, action) => {
        state.stats = action.payload;
      })
      // 获取最近任务
      .addCase(getRecentPomodoroTaskAsync.fulfilled, (state, action) => {
        state.recentTask = action.payload;
      });
  },
});

export const {
  startTimer,
  pauseTimer,
  stopTimer,
  setRemainingTime,
  setCurrentSession,
  incrementSessionCount,
  updateSettings,
  addSession,
  resetTimer,
  setLoading,
} = pomodoroSlice.actions;
export default pomodoroSlice.reducer;