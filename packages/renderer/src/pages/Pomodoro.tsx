import React, { useEffect, useState } from 'react';
import { Card, Button, Progress, Typography, Space, Select, Modal, Form, InputNumber } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, SettingOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { 
  startTimer, 
  pauseTimer, 
  stopTimer, 
  setRemainingTime, 
  updateSettings 
} from '../store/slices/pomodoroSlice';
import { 
  loadPomodoroSessions, 
  loadPomodoroSettingsAsync,
  getRecentPomodoroTaskAsync
} from '../store/thunks/pomodoroThunks';
import { loadTasks } from '../store/thunks/tasksThunks';
import { loadGoals } from '../store/thunks/goalsThunks';

const { Title, Text } = Typography;
const { Option } = Select;

const Pomodoro: React.FC = () => {
  const dispatch = useDispatch();
  const { timer, settings, recentTask } = useSelector((state: RootState) => state.pomodoro);
  const { tasks } = useSelector((state: RootState) => state.tasks);
  const { goals } = useSelector((state: RootState) => state.goals);
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);
  const [form] = Form.useForm();

  // 组件挂载时加载数据
  useEffect(() => {
    dispatch(loadTasks() as any);
    dispatch(loadGoals() as any);
    dispatch(loadPomodoroSessions() as any);
    dispatch(loadPomodoroSettingsAsync() as any);
    dispatch(getRecentPomodoroTaskAsync() as any);
  }, [dispatch]);

  // 智能默认任务选择
  useEffect(() => {
    if (recentTask && tasks.length > 0 && !selectedTaskId) {
      const recentTaskExists = tasks.find(task => task.id === recentTask.taskId);
      if (recentTaskExists) {
        setSelectedTaskId(recentTask.taskId);
        // 找到对应的目标并设置
        const relatedGoal = goals.find(goal => goal.id === recentTaskExists.parentId);
        if (relatedGoal) {
          setSelectedGoalId(relatedGoal.id);
        }
      }
    }
  }, [recentTask, tasks, goals, selectedTaskId]);

  // 根据选择的目标过滤任务
  const filteredTasks = selectedGoalId 
    ? tasks.filter(task => task.parentId === selectedGoalId && task.status !== 'completed')
    : tasks.filter(task => task.status !== 'completed');

  // 处理目标选择变化
  const handleGoalChange = (goalId: string | null) => {
    setSelectedGoalId(goalId);
    // 清空任务选择，让用户重新选择
    setSelectedTaskId(null);
  };

  // 调试：打印任务数据
  useEffect(() => {
    if (tasks.length > 0) {
      console.log('🔍 任务数据调试 - Pomodoro:', {
        任务总数: tasks.length,
        前三个任务: tasks.slice(0, 3).map(task => ({
          id: task.id,
          title: task.title,
          parentId: task.parentId,
          parentType: task.parentType,
          status: task.status,
          完整任务对象: task
        }))
      });
    }
  }, [tasks]);

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 计算进度百分比
  const getProgress = () => {
    const totalTime = settings.workDuration * 60;
    return ((totalTime - timer.remainingTime) / totalTime) * 100;
  };

  // 倒计时逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (timer.isRunning && timer.remainingTime > 0) {
      interval = setInterval(() => {
        dispatch(setRemainingTime(timer.remainingTime - 1));
      }, 1000);
    } else if (timer.remainingTime === 0) {
      // 番茄钟结束
      dispatch(stopTimer());
      // 这里可以添加通知逻辑
      Modal.success({
        title: '番茄钟完成！',
        content: '恭喜完成一个番茄钟，休息一下吧！',
      });
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timer.isRunning, timer.remainingTime, dispatch]);

  const handleStart = () => {
    if (timer.remainingTime === settings.workDuration * 60) {
      // 新开始的番茄钟
      if (!selectedTaskId) {
        Modal.warning({
          title: '请选择任务',
          content: '请先选择要专注的任务',
        });
        return;
      }
    }
    dispatch(startTimer());
  };

  const handlePause = () => {
    dispatch(pauseTimer());
  };

  const handleStop = () => {
    Modal.confirm({
      title: '确认停止',
      content: '确定要停止当前番茄钟吗？进度将丢失。',
      onOk: () => {
        dispatch(stopTimer());
      },
    });
  };

  const handleSettingsSubmit = (values: any) => {
    dispatch(updateSettings(values));
    setIsSettingsVisible(false);
  };

  const showSettings = () => {
    form.setFieldsValue(settings);
    setIsSettingsVisible(true);
  };

  // 获取当前状态描述
  const getStatusText = () => {
    if (timer.isRunning) {
      return '专注中...';
    } else if (timer.isPaused) {
      return '已暂停';
    } else {
      return '准备开始';
    }
  };

  return (
    <div className="lazy-content stable-layout">
      <div className="fast-grid" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1 className="fast-text">番茄工作法</h1>
        <Button className="fast-button" icon={<SettingOutlined />} onClick={showSettings}>
          设置
        </Button>
      </div>

      <div className="fast-grid" style={{ display: 'flex', gap: 24, flexWrap: 'wrap' }}>
        {/* 番茄钟主面板 */}
        <Card className="ultra-fast-card" style={{ flex: 1, minWidth: 400 }}>
          <div style={{ textAlign: 'center' }}>
            <Title level={1} style={{ fontSize: 72, margin: 0, color: timer.isRunning ? '#ff4d4f' : 'var(--color-text)' }}>
              {formatTime(timer.remainingTime)}
            </Title>
            
            <Progress
              type="circle"
              percent={getProgress()}
              format={() => ''}
              strokeColor="var(--color-primary)"
              size={120}
              style={{ margin: '24px 0' }}
            />

            <div style={{ marginBottom: 24 }}>
              <Text type="secondary" style={{ fontSize: 18 }}>
                {getStatusText()}
              </Text>
            </div>

            <Space size="large">
              {!timer.isRunning ? (
                <Button
                  className="fast-button"
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStart}
                >
                  开始
                </Button>
              ) : (
                <Button
                  className="fast-button"
                  size="large"
                  icon={<PauseCircleOutlined />}
                  onClick={handlePause}
                >
                  暂停
                </Button>
              )}
              <Button
                className="fast-button"
                size="large"
                icon={<StopOutlined />}
                onClick={handleStop}
                disabled={timer.remainingTime === settings.workDuration * 60}
              >
                停止
              </Button>
            </Space>
          </div>
        </Card>

        {/* 任务选择和统计 */}
        <Card className="ultra-fast-card" title="目标和任务选择" style={{ width: 300 }}>
          {/* 目标选择器 */}
          <div style={{ marginBottom: 16 }}>
            <Text>选择目标：</Text>
            <Select
              className="fast-input"
              style={{ width: '100%', marginTop: 8 }}
              placeholder="选择目标（可选）"
              value={selectedGoalId}
              onChange={handleGoalChange}
              allowClear
            >
              {goals
                .filter(goal => goal.status === 'active')
                .map(goal => (
                  <Option key={goal.id} value={goal.id}>
                    {goal.name}
                  </Option>
                ))
              }
            </Select>
          </div>

          {/* 任务选择器 */}
          <div style={{ marginBottom: 16 }}>
            <Text>选择要专注的任务：</Text>
            <Select
              className="fast-input"
              style={{ width: '100%', marginTop: 8 }}
              placeholder={selectedGoalId ? "从所选目标中选择任务" : "选择任务"}
              value={selectedTaskId}
              onChange={(value) => {
                console.log('🎯 任务选择变化 (基础版):', value);
                setSelectedTaskId(value);
              }}
              allowClear
              disabled={selectedGoalId && filteredTasks.length === 0}
              showSearch
              filterOption={(input, option) => {
                const task = filteredTasks.find(t => t.id === option?.value);
                const taskTitle = task?.title || '';
                return taskTitle.toLowerCase().includes(input.toLowerCase());
              }}
            >
              {filteredTasks.map(task => {
                // 确保任务标题的正确显示
                const displayTitle = task.title || `任务-${task.id.slice(-8)}` || '未命名任务';
                
                return (
                  <Option key={task.id} value={task.id} title={displayTitle}>
                    <div>
                      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {displayTitle}
                      </div>
                      {!selectedGoalId && (
                        <Text type="secondary" style={{ fontSize: '10px' }}>
                          {goals.find(g => g.id === task.parentId)?.name || task.parentType || '无目标'}
                        </Text>
                      )}
                    </div>
                  </Option>
                );
              })}
            </Select>
            {selectedGoalId && filteredTasks.length === 0 && (
              <Text type="secondary" style={{ fontSize: '11px', marginTop: 4, display: 'block' }}>
                该目标下暂无可用任务
              </Text>
            )}
            {!selectedGoalId && (
              <Text type="secondary" style={{ fontSize: '11px', marginTop: 4, display: 'block' }}>
                显示所有未完成任务，建议先选择目标
              </Text>
            )}
          </div>

          <div style={{ marginTop: 32 }}>
            <Text strong>今日统计</Text>
            <div style={{ marginTop: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>完成番茄数：</Text>
                <Text strong>{timer.sessionCount}</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>专注时长：</Text>
                <Text strong>{timer.sessionCount * settings.workDuration}分钟</Text>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* 设置弹窗 */}
      <Modal
        title="番茄钟设置"
        open={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSettingsSubmit}
        >
          <Form.Item
            name="workDuration"
            label="工作时长（分钟）"
            rules={[{ required: true, message: '请输入工作时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={120} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="shortBreakDuration"
            label="短休息时长（分钟）"
            rules={[{ required: true, message: '请输入短休息时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={30} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="longBreakDuration"
            label="长休息时长（分钟）"
            rules={[{ required: true, message: '请输入长休息时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={60} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="sessionsUntilLongBreak"
            label="长休息前的工作轮数"
            rules={[{ required: true, message: '请输入轮数' }]}
          >
            <InputNumber className="fast-input" min={2} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Pomodoro;