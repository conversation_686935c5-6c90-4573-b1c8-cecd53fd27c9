import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Card, Button, Progress, Typography, Space, Select, Modal, Form, InputNumber, Statistic, Row, Col, List, Tag, Switch } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, SettingOutlined, TrophyOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { 
  startTimer, 
  pauseTimer, 
  stopTimer, 
  setRemainingTime, 
  resetTimer,
  setCurrentSession
} from '../store/slices/pomodoroSlice';
import {
  loadPomodoroSessions,
  createPomodoroSessionAsync,
  completePomodoroSessionAsync,
  loadPomodoroSettingsAsync,
  savePomodoroSettingsAsync,
  getPomodoroStatsAsync,
  getRecentPomodoroTaskAsync
} from '../store/thunks/pomodoroThunks';
import { loadTasks } from '../store/thunks/tasksThunks';
import { loadGoals } from '../store/thunks/goalsThunks';
import { NotificationService } from '../services/NotificationService';
import PomodoroCustomSettings from '../components/PomodoroCustomSettings';
import PomodoroAutoFlow, { PomodoroCompletionCelebration } from '../components/PomodoroAutoFlow';
import { goalBeaconService } from '../services/GoalBeaconService';
import FocusShieldControl from '../components/FocusShieldControl';
import { focusShieldService } from '../services/FocusShieldService';
import { backgroundAudioService } from '../services/BackgroundAudioService';
import BackgroundAudioControl from '../components/BackgroundAudioControl';

const { Title, Text } = Typography;
const { Option } = Select;

const EnhancedPomodoro: React.FC = () => {
  const dispatch = useDispatch();
  const { timer, settings, loading, sessions, stats, recentTask } = useSelector((state: RootState) => state.pomodoro);
  const { tasks } = useSelector((state: RootState) => state.tasks);
  const { goals } = useSelector((state: RootState) => state.goals);
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [sessionType, setSessionType] = useState<'work' | 'short-break' | 'long-break'>('work');
  const [showAutoFlow, setShowAutoFlow] = useState(false);
  const [showCompletion, setShowCompletion] = useState(false);
  const [form] = Form.useForm();
  const notificationService = NotificationService.getInstance();

  // 组件挂载时加载数据
  useEffect(() => {
    dispatch(loadTasks() as any);
    dispatch(loadGoals() as any);
    dispatch(loadPomodoroSessions() as any);
    dispatch(loadPomodoroSettingsAsync() as any);
    dispatch(getPomodoroStatsAsync({
      startDate: new Date(new Date().setHours(0, 0, 0, 0))
    }) as any);
    dispatch(getRecentPomodoroTaskAsync() as any);
  }, [dispatch]);

  // 智能默认任务选择
  useEffect(() => {
    if (recentTask && tasks.length > 0 && !selectedTaskId) {
      const recentTaskExists = tasks.find(task => task.id === recentTask.taskId);
      if (recentTaskExists) {
        setSelectedTaskId(recentTask.taskId);
        // 找到对应的目标并设置
        const relatedGoal = goals.find(goal => goal.id === recentTaskExists.parentId);
        if (relatedGoal) {
          setSelectedGoalId(relatedGoal.id);
        }
      }
    }
  }, [recentTask, tasks, goals, selectedTaskId]);

  // 调试：打印任务数据
  useEffect(() => {
    if (tasks.length > 0) {
      console.log('🔍 任务数据调试 - EnhancedPomodoro:', {
        任务总数: tasks.length,
        前三个任务: tasks.slice(0, 3).map(task => ({
          id: task.id,
          title: task.title,
          parentId: task.parentId,
          parentType: task.parentType,
          status: task.status,
          完整任务对象: task
        }))
      });
    }
  }, [tasks]);

  // 根据选择的目标过滤任务
  const filteredTasks = useMemo(() => {
    if (!selectedGoalId) {
      // 没有选择目标时，显示所有未完成的任务
      return tasks.filter(task => task.status !== 'completed');
    }

    // 选择了目标时，过滤与该目标相关的任务
    // 需要考虑层级关系：目标 -> 子目标 -> 里程碑 -> 任务
    return tasks.filter(task => {
      // 检查任务的状态
      if (task.status === 'completed') {
        return false;
      }

      // 简化处理：任务的parentId直接等于选中的目标ID
      // 在真实场景中，这里应该通过层级查询（目标->sub_goals->milestones->tasks）
      const isDirectMatch = task.parentId === selectedGoalId;
      
      if (isDirectMatch) {
        console.log('🔗 目标-任务匹配:', {
          任务ID: task.id,
          任务标题: task.title,
          任务父级ID: task.parentId,
          任务父级类型: task.parentType,
          选中目标ID: selectedGoalId,
          状态: task.status,
          匹配结果: true
        });
      }
      
      return isDirectMatch;
    });
  }, [selectedGoalId, tasks]);

  // 处理目标选择变化
  const handleGoalChange = (goalId: string | null) => {
    setSelectedGoalId(goalId);
    // 清空任务选择，让用户重新选择
    setSelectedTaskId(null);
  };

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 计算进度百分比
  const getProgress = () => {
    const totalTime = getDurationForSessionType(sessionType) * 60;
    return ((totalTime - timer.remainingTime) / totalTime) * 100;
  };

  // 倒计时逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (timer.isRunning && timer.remainingTime > 0) {
      interval = setInterval(() => {
        dispatch(setRemainingTime(timer.remainingTime - 1));
      }, 1000);
    } else if (timer.remainingTime === 0 && timer.isRunning) {
      // 番茄钟结束
      handleSessionComplete();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timer.isRunning, timer.remainingTime, dispatch]);

  // 获取会话类型对应的时长
  const getDurationForSessionType = (type: string) => {
    switch (type) {
      case 'work':
        return settings.workDuration;
      case 'short-break':
        return settings.shortBreakDuration;
      case 'long-break':
        return settings.longBreakDuration;
      default:
        return settings.workDuration;
    }
  };

  // 创建新的番茄钟会话
  const createNewSession = async () => {
    const sessionData = {
      taskId: selectedTaskId || '',
      type: sessionType,
      startTime: new Date(),
      duration: getDurationForSessionType(sessionType)
    };
    
    const result = await dispatch(createPomodoroSessionAsync(sessionData) as any);
    if (result.payload) {
      setCurrentSessionId(result.payload.id);
    }
  };

  // 处理会话完成
  const handleSessionComplete = async () => {
    dispatch(stopTimer());

    // 停止背景音频
    try {
      await backgroundAudioService.stopBackgroundSound();
    } catch (error) {
      console.error('停止背景音频失败:', error);
    }

    // 完成当前会话
    if (currentSessionId) {
      await dispatch(completePomodoroSessionAsync({
        sessionId: currentSessionId,
        actualDuration: getDurationForSessionType(sessionType),
        wasInterrupted: false
      }) as any);
    }

    // 发送完成通知
    await notificationService.notifyPomodoroComplete(
      sessionType === 'work' ? 'work' : 'break',
      timer.sessionCount + (sessionType === 'work' ? 1 : 0)
    );

    // 显示完成庆祝
    setShowCompletion(true);
  };

  // 切换到下一个会话类型
  const switchToNextSession = () => {
    if (sessionType === 'work') {
      const shouldLongBreak = (timer.sessionCount + 1) % settings.sessionsUntilLongBreak === 0;
      const nextType = shouldLongBreak ? 'long-break' : 'short-break';
      setSessionType(nextType);
      dispatch(setRemainingTime(getDurationForSessionType(nextType) * 60));
    } else {
      setSessionType('work');
      dispatch(setRemainingTime(settings.workDuration * 60));
    }

    setCurrentSessionId(null);

    // 显示自动流转界面
    setShowAutoFlow(true);
  };

  // 处理完成庆祝后的下一步
  const handleCompletionNext = () => {
    setShowCompletion(false);
    switchToNextSession();
  };

  const handleStart = async () => {
    if (timer.remainingTime === getDurationForSessionType(sessionType) * 60) {
      // 新开始的番茄钟
      if (!selectedTaskId && sessionType === 'work') {
        Modal.warning({
          title: '请选择任务',
          content: '请先选择要专注的任务',
        });
        return;
      }
      
      // 创建新的番茄钟会话
      await createNewSession();
    }
    
    dispatch(startTimer());

    // 启动背景音频
    try {
      if (sessionType === 'work') {
        await backgroundAudioService.startWorkSession();
      } else {
        await backgroundAudioService.startBreakSession();
      }
    } catch (error) {
      console.error('启动背景音频失败:', error);
    }

    // 启动Focus Shield (如果启用且在工作时段)
    if (sessionType === 'work') {
      try {
        await focusShieldService.start('pomodoro');
      } catch (error) {
        console.error('启动Focus Shield失败:', error);
      }
    }
    
    // 发送开始通知
    if (sessionType === 'work' && selectedTaskId) {
      const task = tasks.find(t => t.id === selectedTaskId);
      if (task) {
        notificationService.notifyPomodoroStart(task.title);
      }
    }
  };

  const handlePause = async () => {
    dispatch(pauseTimer());
    notificationService.notifyPomodoroPause();

    // 暂停背景音频
    try {
      await backgroundAudioService.pauseSession();
    } catch (error) {
      console.error('暂停背景音频失败:', error);
    }

    // 暂停Focus Shield
    focusShieldService.pause();
  };

  const handleStop = () => {
    Modal.confirm({
      title: '确认停止',
      content: '确定要停止当前番茄钟吗？进度将丢失。',
      onOk: async () => {
        // 如果有当前会话，标记为中断
        if (currentSessionId) {
          await dispatch(completePomodoroSessionAsync({
            sessionId: currentSessionId,
            actualDuration: Math.floor((getDurationForSessionType(sessionType) * 60 - timer.remainingTime) / 60),
            wasInterrupted: true
          }) as any);
        }
        
        dispatch(resetTimer());
        setCurrentSessionId(null);
        setSessionType('work');

        // 停止背景音频
        try {
          await backgroundAudioService.stopBackgroundSound();
        } catch (error) {
          console.error('停止背景音频失败:', error);
        }

        // 停止Focus Shield
        focusShieldService.stop();
      },
    });
  };

  const handleSettingsSubmit = async (values: any) => {
    await dispatch(savePomodoroSettingsAsync(values) as any);
    setIsSettingsVisible(false);
  };

  const showSettings = () => {
    setIsSettingsVisible(true);
  };

  // 处理自动流转开始
  const handleAutoFlowStart = async () => {
    setShowAutoFlow(false);

    // 如果是休息时段，触发目标提醒
    if (sessionType !== 'work') {
      goalBeaconService.triggerGoalBeacon('break', `开始${sessionType === 'short-break' ? '短' : '长'}休息`);
    }

    await handleStart();
  };

  // 处理跳过当前会话
  const handleSkipSession = () => {
    setShowAutoFlow(false);
    switchToNextSession();
  };

  // 获取当前状态描述
  const getStatusText = () => {
    if (timer.isRunning) {
      return sessionType === 'work' ? '专注中...' : '休息中...';
    } else if (timer.isPaused) {
      return '已暂停';
    } else {
      const typeText = {
        'work': '准备开始工作',
        'short-break': '准备短休息',
        'long-break': '准备长休息'
      };
      return typeText[sessionType] || '准备开始';
    }
  };

  // 获取会话类型显示文本
  const getSessionTypeText = () => {
    const typeText = {
      'work': '工作时间',
      'short-break': '短休息',
      'long-break': '长休息'
    };
    return typeText[sessionType] || '工作时间';
  };

  // 获取今日统计
  const getTodayStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todaySessions = sessions.filter(s => 
      s.startTime && new Date(s.startTime).toISOString().split('T')[0] === today
    );
    
    const completedWorkSessions = todaySessions.filter(s => 
      s.type === 'work' && s.isCompleted
    ).length;
    
    const totalFocusTime = todaySessions
      .filter(s => s.type === 'work' && s.isCompleted)
      .reduce((total, session) => total + session.duration, 0);
    
    return {
      completedSessions: completedWorkSessions,
      totalFocusTime
    };
  };

  const todayStats = getTodayStats();

  return (
    <div
      className="lazy-content stable-layout"
      style={{
        width: '100%',
        minHeight: '100%',
        overflow: 'visible' // 允许内容溢出，由父容器处理滚动
      }}
    >
      <div className="fast-grid" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1 className="fast-text">🍅 番茄工作法</h1>
        <Button className="fast-button" icon={<SettingOutlined />} onClick={showSettings}>
          设置
        </Button>
      </div>

      {/* 今日统计 */}
      <Row className="fast-grid" gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card
            className="ultra-fast-card"
            size="small"
            style={{
              height: '100%',
              minHeight: '120px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <Statistic
              title="今日番茄"
              value={todayStats.completedSessions}
              prefix={<TrophyOutlined style={{ fontSize: '18px' }} />}
              valueStyle={{
                color: '#cf1322',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card
            className="ultra-fast-card"
            size="small"
            style={{
              height: '100%',
              minHeight: '120px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <Statistic
              title="专注时长"
              value={todayStats.totalFocusTime}
              suffix="分钟"
              prefix={<ClockCircleOutlined style={{ fontSize: '18px' }} />}
              valueStyle={{
                color: '#1890ff',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card
            className="ultra-fast-card"
            size="small"
            style={{
              height: '100%',
              minHeight: '120px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <Statistic
              title="效率"
              value={todayStats.completedSessions > 0 ? Math.round((todayStats.totalFocusTime / (todayStats.completedSessions * 25)) * 100) : 0}
              suffix="%"
              valueStyle={{
                color: '#52c41a',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card
            className="ultra-fast-card"
            size="small"
            style={{
              height: '100%',
              minHeight: '120px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <Statistic
              title="连续天数"
              value={3}
              valueStyle={{
                color: '#722ed1',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
          </Card>
        </Col>
      </Row>

      <Row className="fast-grid" gutter={[24, 24]}>
        <Col xs={24} sm={24} md={24} lg={16} xl={16}>
          {/* 番茄钟主面板 */}
          <Card
            className="ultra-fast-card"
            style={{
              width: '100%',
              height: '100%',
              overflow: 'hidden'
            }}
          >
          <div style={{ textAlign: 'center', padding: '0 16px' }}>
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary" style={{ fontSize: 18 }}>
                {getSessionTypeText()}
              </Text>
            </div>

            {/* 响应式时间显示 */}
            <div style={{
              marginBottom: 24,
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '80px'
            }}>
              <Title
                level={1}
                style={{
                  fontSize: 'clamp(48px, 8vw, 72px)', // 响应式字体大小
                  margin: 0,
                  color: timer.isRunning
                    ? (sessionType === 'work' ? '#ff4d4f' : '#52c41a')
                    : '#666',
                  lineHeight: 1,
                  fontFamily: 'monospace', // 等宽字体确保对齐
                  letterSpacing: '2px',
                  whiteSpace: 'nowrap' // 防止换行
                }}
              >
                {formatTime(timer.remainingTime)}
              </Title>
            </div>

            <Progress
              type="circle"
              percent={getProgress()}
              format={() => ''}
              strokeColor={{
                '0%': sessionType === 'work' ? '#ff4d4f' : '#52c41a',
                '100%': sessionType === 'work' ? '#87d068' : '#108ee9',
              }}
              size={120}
              style={{ margin: '24px 0' }}
            />

            <div style={{ marginBottom: 24 }}>
              <Text type="secondary" style={{ fontSize: 18 }}>
                {getStatusText()}
              </Text>
            </div>

            <Space size="large">
              {!timer.isRunning ? (
                <Button
                  className="fast-button"
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStart}
                  loading={loading}
                >
                  开始
                </Button>
              ) : (
                <Button
                  className="fast-button"
                  size="large"
                  icon={<PauseCircleOutlined />}
                  onClick={handlePause}
                >
                  暂停
                </Button>
              )}
              <Button
                className="fast-button"
                size="large"
                icon={<StopOutlined />}
                onClick={handleStop}
                disabled={timer.remainingTime === getDurationForSessionType(sessionType) * 60}
              >
                停止
              </Button>
            </Space>
          </div>
        </Card>
        </Col>

        <Col xs={24} sm={24} md={24} lg={8} xl={8}>
          {/* 任务选择和历史 */}
          <Card
            className="ultra-fast-card"
            title="任务与历史"
            style={{
              width: '100%',
              height: '100%'
            }}
          >
          {sessionType === 'work' && (
            <div style={{ marginBottom: 24 }}>
              {/* 目标选择器 */}
              <div style={{ marginBottom: 16 }}>
                <Text>选择目标：</Text>
                <Select
                  className="fast-input"
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder="选择目标（可选）"
                  value={selectedGoalId}
                  onChange={handleGoalChange}
                  allowClear
                  loading={loading}
                >
                  {goals
                    .filter(goal => goal.status === 'active')
                    .map(goal => (
                      <Option key={goal.id} value={goal.id}>
                        {goal.name}
                      </Option>
                    ))
                  }
                </Select>
              </div>

              {/* 任务选择器 */}
              <div>
                <Text>选择要专注的任务：</Text>
                <Select
                  className="fast-input"
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder={selectedGoalId ? "从所选目标中选择任务" : "选择任务"}
                  value={selectedTaskId}
                  onChange={(value) => {
                    console.log('🎯 任务选择变化:', value);
                    setSelectedTaskId(value);
                  }}
                  allowClear
                  loading={loading}
                  disabled={false}
                  showSearch
                  filterOption={(input, option) => {
                    const task = filteredTasks.find(t => t.id === option?.value);
                    const taskTitle = task?.title || '';
                    return taskTitle.toLowerCase().includes(input.toLowerCase());
                  }}
                >
                  {filteredTasks.map(task => {
                    // 确保任务标题的正确显示
                    const displayTitle = task.title || `任务-${task.id.slice(-8)}` || '未命名任务';
                    
                    return (
                      <Option key={task.id} value={task.id} title={displayTitle}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                            {displayTitle}
                          </span>
                          {!selectedGoalId && (
                            <Text type="secondary" style={{ fontSize: '12px', marginLeft: 8, flexShrink: 0 }}>
                              {task.parentType === 'subgoal' ? '子目标' : 
                               task.parentType === 'milestone' ? '里程碑' :
                               goals.find(g => g.id === task.parentId)?.name || '目标'
                              }
                            </Text>
                          )}
                        </div>
                      </Option>
                    );
                  })}
                </Select>
                {selectedGoalId && filteredTasks.length === 0 && (
                  <Text type="secondary" style={{ fontSize: '12px', marginTop: 4, display: 'block' }}>
                    该目标下暂无可用任务
                  </Text>
                )}
                {!selectedGoalId && (
                  <Text type="secondary" style={{ fontSize: '12px', marginTop: 4, display: 'block' }}>
                    显示所有未完成任务，建议先选择目标
                  </Text>
                )}
                {filteredTasks.length > 0 && (
                  <Text type="secondary" style={{ fontSize: '11px', marginTop: 4, display: 'block' }}>
                    共 {filteredTasks.length} 个可用任务
                  </Text>
                )}
              </div>
            </div>
          )}

          <div>
            <Text strong>最近会话</Text>
            <List
              className="fast-grid"
              size="small"
              dataSource={sessions.slice(0, 5)}
              style={{ marginTop: 16, maxHeight: 300, overflowY: 'auto' }}
              renderItem={(session) => (
                <List.Item className="fast-list-item">
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <Tag color={session.type === 'work' ? 'red' : 'green'}>
                          {session.type === 'work' ? '工作' : '休息'}
                        </Tag>
                        <Text style={{ fontSize: 12 }}>
                          {session.duration}分钟
                        </Text>
                      </div>
                      <div>
                        {session.isCompleted ? (
                          <Tag color="success">完成</Tag>
                        ) : session.wasInterrupted ? (
                          <Tag color="warning">中断</Tag>
                        ) : (
                          <Tag color="processing">进行中</Tag>
                        )}
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        </Card>
        </Col>
      </Row>

      {/* Focus Shield 控制面板 */}
      <Row className="fast-grid" style={{ marginTop: 24 }}>
        <Col span={24}>
          <FocusShieldControl embedded={true} showFullControls={false} />
        </Col>
      </Row>

      {/* 背景音频控制面板 */}
      <Row className="fast-grid" style={{ marginTop: 16 }}>
        <Col span={24}>
          <BackgroundAudioControl embedded={true} showFullControls={false} />
        </Col>
      </Row>

      {/* 设置弹窗 */}
      <Modal
        title="番茄钟设置"
        open={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSettingsSubmit}
        >
          <Form.Item
            name="workDuration"
            label="工作时长（分钟）"
            rules={[{ required: true, message: '请输入工作时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={120} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="shortBreakDuration"
            label="短休息时长（分钟）"
            rules={[{ required: true, message: '请输入短休息时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={30} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="longBreakDuration"
            label="长休息时长（分钟）"
            rules={[{ required: true, message: '请输入长休息时长' }]}
          >
            <InputNumber className="fast-input" min={1} max={60} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="sessionsUntilLongBreak"
            label="长休息前的工作轮数"
            rules={[{ required: true, message: '请输入轮数' }]}
          >
            <InputNumber className="fast-input" min={2} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="autoStartBreaks"
            label="自动开始休息"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="autoStartNextSession"
            label="自动开始下一个会话"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 自定义设置模态框 */}
      <PomodoroCustomSettings
        visible={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        onSave={handleSettingsSubmit}
        initialSettings={settings}
      />

      {/* 自动流转模态框 */}
      <PomodoroAutoFlow
        visible={showAutoFlow}
        sessionType={sessionType}
        remainingTime={timer.remainingTime}
        totalTime={getDurationForSessionType(sessionType)}
        sessionCount={timer.sessionCount}
        onStart={handleAutoFlowStart}
        onSkip={handleSkipSession}
        onClose={() => setShowAutoFlow(false)}
        autoStartEnabled={settings.autoStartBreaks || settings.autoStartNextSession}
        autoStartDelay={5}
      />

      {/* 完成庆祝模态框 */}
      <PomodoroCompletionCelebration
        visible={showCompletion}
        sessionType={sessionType}
        sessionCount={timer.sessionCount}
        onNext={handleCompletionNext}
        onClose={() => setShowCompletion(false)}
      />
    </div>
  );
};

export default EnhancedPomodoro;